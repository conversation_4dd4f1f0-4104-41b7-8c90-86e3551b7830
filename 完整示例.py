#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦龙虎榜爬虫完整示例
展示如何使用TCP协议获取真实数据
"""

from kaipanla_longhu_spider import KaiPanLaLongHuSpider
import time
import json

def demo_realtime_data():
    """演示获取实时龙虎榜数据"""
    print("=== 获取实时龙虎榜数据 ===")
    
    spider = KaiPanLaLongHuSpider()
    
    try:
        # 获取实时数据
        data = spider.get_realtime_longhu()
        
        if data:
            print(f"✓ 成功获取 {len(data)} 条实时龙虎榜数据")
            
            # 显示数据概览
            print("\n数据概览:")
            for i, item in enumerate(data[:5]):  # 显示前5条
                print(f"{i+1:2d}. {item}")
            
            # 保存数据
            spider.save_to_csv(data, 'realtime_longhu')
            spider.save_to_json(data, 'realtime_longhu')
            print(f"\n✓ 数据已保存到 data/ 目录")
            
            return data
        else:
            print("✗ 未获取到实时数据")
            return []
            
    except Exception as e:
        print(f"✗ 获取实时数据失败: {e}")
        return []

def demo_daily_data():
    """演示获取日度龙虎榜数据"""
    print("\n=== 获取今日龙虎榜数据 ===")
    
    spider = KaiPanLaLongHuSpider()
    
    try:
        # 获取今日数据
        data = spider.get_daily_longhu()
        
        if data:
            print(f"✓ 成功获取 {len(data)} 条今日龙虎榜数据")
            
            # 保存数据
            spider.save_to_csv(data, 'daily_longhu')
            spider.save_to_json(data, 'daily_longhu')
            print(f"✓ 数据已保存到 data/ 目录")
            
            return data
        else:
            print("✗ 未获取到今日数据")
            return []
            
    except Exception as e:
        print(f"✗ 获取今日数据失败: {e}")
        return []

def demo_stock_detail():
    """演示获取股票详情"""
    print("\n=== 获取股票龙虎榜详情 ===")
    
    spider = KaiPanLaLongHuSpider()
    
    # 测试几个热门股票
    test_stocks = ['000001', '000002', '600519', '000858']
    
    for stock_code in test_stocks:
        try:
            print(f"\n查询股票: {stock_code}")
            data = spider.get_longhu_detail(stock_code)
            
            if data:
                print(f"✓ 成功获取股票 {stock_code} 的龙虎榜详情")
                print(f"详情数据: {data}")
                
                # 保存数据
                spider.save_to_json(data, f'stock_detail_{stock_code}')
            else:
                print(f"✗ 未获取到股票 {stock_code} 的详情")
                
        except Exception as e:
            print(f"✗ 获取股票 {stock_code} 详情失败: {e}")
        
        time.sleep(1)  # 避免请求过快

def demo_monitoring():
    """演示实时监控模式"""
    print("\n=== 实时监控模式演示 ===")
    print("将每30秒获取一次数据，共监控3轮")
    
    spider = KaiPanLaLongHuSpider()
    
    for round_num in range(1, 4):
        print(f"\n--- 第 {round_num} 轮监控 ---")
        
        try:
            data = spider.get_realtime_longhu()
            
            if data:
                print(f"✓ 获取到 {len(data)} 条数据")
                
                # 保存数据
                timestamp = time.strftime('%Y%m%d_%H%M%S')
                spider.save_to_json(data, f'monitor_{timestamp}')
                
                # 显示简要信息
                print("热门股票:")
                for i, item in enumerate(data[:3]):
                    print(f"  {i+1}. {item}")
            else:
                print("✗ 本轮未获取到数据")
                
        except Exception as e:
            print(f"✗ 第 {round_num} 轮监控失败: {e}")
        
        if round_num < 3:
            print("等待30秒...")
            time.sleep(30)
    
    print("\n✓ 监控演示完成")

def test_tcp_connection():
    """测试TCP连接"""
    print("=== TCP连接测试 ===")
    
    spider = KaiPanLaLongHuSpider()
    
    print("测试连接到开盘啦TCP服务器...")
    if spider.connect():
        print("✓ TCP连接成功")
        print(f"服务器: {spider.tcp_host}:{spider.tcp_port}")
        spider.disconnect()
        return True
    else:
        print("✗ TCP连接失败")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. 服务器地址变更")
        print("3. 防火墙阻止连接")
        return False

def main():
    """主演示程序"""
    print("开盘啦龙虎榜爬虫完整演示")
    print("=" * 50)
    print("本演示将展示如何使用TCP协议获取开盘啦的真实龙虎榜数据")
    print("=" * 50)
    
    # 1. 测试TCP连接
    if not test_tcp_connection():
        print("\n❌ TCP连接失败，无法继续演示")
        return
    
    # 2. 获取实时数据
    realtime_data = demo_realtime_data()
    
    # 3. 获取日度数据
    daily_data = demo_daily_data()
    
    # 4. 获取股票详情
    demo_stock_detail()
    
    # 5. 询问是否演示监控模式
    try:
        choice = input("\n是否演示实时监控模式？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            demo_monitoring()
    except:
        pass
    
    # 总结
    print("\n" + "=" * 50)
    print("演示完成！")
    print("=" * 50)
    
    if realtime_data or daily_data:
        print("✅ 成功获取到真实数据")
        print("📁 数据文件已保存到 data/ 目录")
        print("📊 可以使用Excel或其他工具查看CSV文件")
        print("🔧 可以使用JSON文件进行进一步处理")
    else:
        print("⚠️  未获取到数据，可能的原因:")
        print("   1. 网络连接问题")
        print("   2. 服务器暂时不可用")
        print("   3. 协议参数需要调整")
    
    print("\n📖 更多使用方法请查看:")
    print("   - README.md")
    print("   - 使用说明.md")
    print("   - kaipanla_longhu_spider.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断演示")
    except Exception as e:
        print(f"\n演示过程中出错: {e}")
        print("请检查网络连接和Python环境")
