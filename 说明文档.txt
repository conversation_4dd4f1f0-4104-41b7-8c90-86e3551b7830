开盘啦数据爬虫项目说明

项目概述：
本项目是一个专门用于爬取开盘啦网站股票数据的Python爬虫工具。
开盘啦是一个专业的股票数据分析平台，提供实时行情、概念板块、标签等信息。

主要功能：
1. 爬取股票基本信息（代码、名称、价格等）
2. 获取股票标签和概念板块信息
3. 支持多线程并发爬取，提高效率
4. 多种数据输出格式（CSV、JSON、Excel、SQLite）
5. 智能反爬虫策略（随机延时、请求头伪装）
6. 完整的日志记录和错误处理

文件结构：
- kaipanla_spider.py          # 基础爬虫脚本
- advanced_kaipanla_spider.py # 高级爬虫脚本（推荐）
- config.py                   # 配置文件
- run_spider.py              # 运行脚本（命令行界面）
- run.bat                    # Windows批处理文件
- requirements.txt           # Python依赖包列表
- stock_list.txt            # 股票代码列表
- README.md                 # 详细说明文档

使用方法：
1. 安装Python（建议3.8+版本）
2. 双击运行 run.bat（Windows）或执行 python run_spider.py
3. 按照提示选择运行模式
4. 等待爬取完成，查看生成的数据文件

注意事项：
1. 请合理使用，避免对服务器造成过大压力
2. 数据仅供参考，投资决策请以官方数据为准
3. 遵守相关法律法规和网站使用条款
