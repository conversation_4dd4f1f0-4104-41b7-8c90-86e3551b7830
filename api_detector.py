#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦API接口探测器
自动发现和测试开盘啦的真实API接口
"""

import urllib.request
import urllib.parse
import json
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class KaiPanLaAPIDetector:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 kaipanla/6.8.0',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Content-Type': 'application/x-www-form-urlencoded',
        }
        
        # 可能的API基础URL
        self.base_urls = [
            'https://www.kaipanla.com/w1/api/index.php',
            'https://api.kaipanla.com/api/v1',
            'https://app.kaipanla.com/api',
            'https://m.kaipanla.com/api',
            'https://kpl-api.kaipanla.com',
        ]
        
        # 可能的龙虎榜API参数组合
        self.api_patterns = [
            # 基于已知信息的参数组合
            {'v': 'w21', 'c': 'FuPanLa', 'type': 'longhu'},
            {'v': 'w21', 'c': 'FuPanLa', 'action': 'longhu'},
            {'v': 'w21', 'c': 'FuPanLa', 'method': 'longhu'},
            {'v': 'w21', 'c': 'FuPanLa', 'api': 'longhu'},
            
            # 其他可能的参数组合
            {'action': 'longhu', 'type': 'realtime'},
            {'method': 'getLongHu', 'format': 'json'},
            {'api': 'longhu', 'version': '1.0'},
            {'service': 'longhu', 'format': 'json'},
            
            # 简单参数
            {'longhu': '1'},
            {'type': 'longhu'},
            {'action': 'longhu'},
        ]
    
    def make_request(self, url, params=None, method='GET'):
        """发送HTTP请求"""
        try:
            if method == 'GET' and params:
                url_params = urllib.parse.urlencode(params)
                url = f"{url}?{url_params}"
                req = urllib.request.Request(url, headers=self.headers)
            elif method == 'POST' and params:
                data = urllib.parse.urlencode(params).encode('utf-8')
                req = urllib.request.Request(url, data=data, headers=self.headers)
            else:
                req = urllib.request.Request(url, headers=self.headers)
            
            with urllib.request.urlopen(req, timeout=10) as response:
                if response.status == 200:
                    content = response.read().decode('utf-8')
                    try:
                        return json.loads(content)
                    except:
                        return content
                else:
                    return None
                    
        except Exception as e:
            logging.debug(f"请求失败 {url}: {str(e)}")
            return None
    
    def test_api_endpoint(self, base_url, params, method='GET'):
        """测试单个API端点"""
        try:
            result = self.make_request(base_url, params, method)
            
            if result:
                # 检查返回结果是否包含龙虎榜相关数据
                result_str = str(result).lower()
                longhu_keywords = ['龙虎榜', 'longhu', 'dragon', 'tiger', '涨停', 'stock', '股票']
                
                keyword_count = sum(1 for keyword in longhu_keywords if keyword in result_str)
                
                if keyword_count > 0:
                    logging.info(f"✓ 发现可能的龙虎榜API: {base_url}")
                    logging.info(f"  参数: {params}")
                    logging.info(f"  方法: {method}")
                    logging.info(f"  关键词匹配: {keyword_count}")
                    logging.info(f"  返回数据预览: {str(result)[:200]}...")
                    return True, result
            
            return False, None
            
        except Exception as e:
            logging.debug(f"测试API失败: {str(e)}")
            return False, None
    
    def detect_longhu_apis(self):
        """探测龙虎榜API接口"""
        logging.info("开始探测开盘啦龙虎榜API接口...")
        
        found_apis = []
        
        for base_url in self.base_urls:
            logging.info(f"测试基础URL: {base_url}")
            
            for params in self.api_patterns:
                # 测试GET请求
                success, result = self.test_api_endpoint(base_url, params, 'GET')
                if success:
                    found_apis.append({
                        'url': base_url,
                        'params': params,
                        'method': 'GET',
                        'result': result
                    })
                
                # 测试POST请求
                success, result = self.test_api_endpoint(base_url, params, 'POST')
                if success:
                    found_apis.append({
                        'url': base_url,
                        'params': params,
                        'method': 'POST',
                        'result': result
                    })
                
                # 避免请求过快
                time.sleep(0.5)
        
        return found_apis
    
    def test_specific_endpoints(self):
        """测试特定的已知端点"""
        logging.info("测试特定的已知API端点...")
        
        specific_tests = [
            {
                'url': 'https://www.kaipanla.com/w1/api/index.php',
                'params': {'v': 'w21', 'c': 'FuPanLa'},
                'method': 'POST'
            },
            {
                'url': 'https://www.kaipanla.com/w1/api/index.php',
                'params': {'v': 'w21', 'c': 'FuPanLa', 'type': 'longhu'},
                'method': 'POST'
            }
        ]
        
        results = []
        for test in specific_tests:
            success, result = self.test_api_endpoint(test['url'], test['params'], test['method'])
            if success:
                results.append({
                    'test': test,
                    'result': result
                })
        
        return results
    
    def generate_spider_code(self, found_apis):
        """根据发现的API生成爬虫代码"""
        if not found_apis:
            logging.warning("未发现有效的API接口")
            return
        
        logging.info("生成爬虫代码...")
        
        # 选择最佳API
        best_api = found_apis[0]
        
        code_template = f'''
# 根据API探测结果生成的爬虫代码
class KaiPanLaRealSpider:
    def __init__(self):
        self.api_url = "{best_api['url']}"
        self.api_params = {best_api['params']}
        self.method = "{best_api['method']}"
        
    def get_longhu_data(self):
        # 使用发现的API获取龙虎榜数据
        # 实现具体的数据获取逻辑
        pass
'''
        
        with open('generated_spider.py', 'w', encoding='utf-8') as f:
            f.write(code_template)
        
        logging.info("爬虫代码已生成到 generated_spider.py")

def main():
    """主函数"""
    print("=== 开盘啦API接口探测器 ===")
    
    detector = KaiPanLaAPIDetector()
    
    # 探测API接口
    found_apis = detector.detect_longhu_apis()
    
    # 测试特定端点
    specific_results = detector.test_specific_endpoints()
    
    # 汇总结果
    all_results = found_apis + specific_results
    
    if all_results:
        print(f"\n✓ 发现 {len(all_results)} 个可能的API接口:")
        for i, api in enumerate(all_results):
            print(f"{i+1}. URL: {api.get('url', api.get('test', {}).get('url'))}")
            print(f"   参数: {api.get('params', api.get('test', {}).get('params'))}")
            print(f"   方法: {api.get('method', api.get('test', {}).get('method'))}")
            print()
        
        # 生成爬虫代码
        detector.generate_spider_code(all_results)
    else:
        print("\n✗ 未发现有效的API接口")
        print("可能的原因:")
        print("1. API接口需要认证")
        print("2. 请求头不正确")
        print("3. 参数组合不对")
        print("4. 接口地址已变更")

if __name__ == "__main__":
    main()
