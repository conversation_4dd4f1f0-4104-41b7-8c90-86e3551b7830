#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦龙虎榜爬虫演示程序
使用模拟数据展示功能
"""

import json
import time
import random
from datetime import datetime, timedelta
from kaipanla_longhu_spider import KaiPanLaLongHuSpider

def generate_mock_longhu_data(count=10):
    """生成模拟的龙虎榜数据"""
    
    # 模拟股票池
    stocks = [
        {'code': '000001', 'name': '平安银行'},
        {'code': '000002', 'name': '万科A'},
        {'code': '000858', 'name': '五粮液'},
        {'code': '002415', 'name': '海康威视'},
        {'code': '300059', 'name': '东方财富'},
        {'code': '600036', 'name': '招商银行'},
        {'code': '600519', 'name': '贵州茅台'},
        {'code': '600887', 'name': '伊利股份'},
        {'code': '000858', 'name': '五粮液'},
        {'code': '002594', 'name': '比亚迪'},
    ]
    
    # 上榜原因
    reasons = [
        '日涨幅偏离值达7%',
        '日跌幅偏离值达7%',
        '日换手率达20%',
        '连续三个交易日内涨幅偏离值累计达20%',
        '连续三个交易日内跌幅偏离值累计达20%',
        '日成交金额达5000万元',
    ]
    
    mock_data = []
    
    for i in range(count):
        stock = random.choice(stocks)
        
        # 生成随机数据
        base_price = random.uniform(5.0, 200.0)
        change_percent = random.uniform(-10.0, 10.0)
        current_price = base_price * (1 + change_percent / 100)
        
        item = {
            'code': stock['code'],
            'name': stock['name'],
            'price': round(current_price, 2),
            'change_percent': round(change_percent, 2),
            'amount': random.randint(50000000, 2000000000),  # 成交金额
            'volume': random.randint(1000000, 100000000),    # 成交量
            'reason': random.choice(reasons),
            'date': datetime.now().strftime('%Y-%m-%d'),
            'time': datetime.now().strftime('%H:%M:%S'),
            'rank': i + 1,
            'buy_amount': random.randint(10000000, 500000000),   # 买入金额
            'sell_amount': random.randint(10000000, 500000000),  # 卖出金额
            'net_amount': random.randint(-100000000, 100000000), # 净买入
        }
        
        mock_data.append(item)
    
    return mock_data

def demo_basic_functions():
    """演示基本功能"""
    print("=== 开盘啦龙虎榜爬虫功能演示 ===\n")
    
    # 创建爬虫实例
    spider = KaiPanLaLongHuSpider()
    
    # 生成模拟数据
    print("1. 生成模拟龙虎榜数据...")
    mock_data = generate_mock_longhu_data(15)
    print(f"✓ 生成了 {len(mock_data)} 条模拟数据\n")
    
    # 显示部分数据
    print("2. 数据预览（前5条）:")
    print("-" * 80)
    for i, item in enumerate(mock_data[:5]):
        print(f"{i+1:2d}. {item['name']} ({item['code']}) "
              f"价格:{item['price']:6.2f} 涨幅:{item['change_percent']:+5.1f}% "
              f"成交额:{item['amount']:>10,} 原因:{item['reason']}")
    print("-" * 80)
    print()
    
    # 保存为CSV
    print("3. 保存数据为CSV格式...")
    if spider.save_to_csv(mock_data, 'demo_longhu'):
        print("✓ CSV文件保存成功\n")
    else:
        print("✗ CSV文件保存失败\n")
    
    # 保存为JSON
    print("4. 保存数据为JSON格式...")
    if spider.save_to_json(mock_data, 'demo_longhu'):
        print("✓ JSON文件保存成功\n")
    else:
        print("✗ JSON文件保存失败\n")
    
    return mock_data

def demo_data_analysis(data):
    """演示数据分析功能"""
    print("=== 数据分析演示 ===\n")
    
    # 统计涨跌情况
    up_count = len([x for x in data if x['change_percent'] > 0])
    down_count = len([x for x in data if x['change_percent'] < 0])
    flat_count = len(data) - up_count - down_count
    
    print(f"涨跌统计:")
    print(f"  上涨: {up_count} 只 ({up_count/len(data)*100:.1f}%)")
    print(f"  下跌: {down_count} 只 ({down_count/len(data)*100:.1f}%)")
    print(f"  平盘: {flat_count} 只 ({flat_count/len(data)*100:.1f}%)")
    print()
    
    # 成交金额统计
    total_amount = sum(x['amount'] for x in data)
    avg_amount = total_amount / len(data)
    max_amount = max(x['amount'] for x in data)
    min_amount = min(x['amount'] for x in data)
    
    print(f"成交金额统计:")
    print(f"  总成交额: {total_amount:,} 元")
    print(f"  平均成交额: {avg_amount:,.0f} 元")
    print(f"  最大成交额: {max_amount:,} 元")
    print(f"  最小成交额: {min_amount:,} 元")
    print()
    
    # 涨幅排行
    sorted_by_change = sorted(data, key=lambda x: x['change_percent'], reverse=True)
    print("涨幅排行榜（前5名）:")
    for i, item in enumerate(sorted_by_change[:5]):
        print(f"  {i+1}. {item['name']} ({item['code']}) {item['change_percent']:+5.1f}%")
    print()
    
    # 成交额排行
    sorted_by_amount = sorted(data, key=lambda x: x['amount'], reverse=True)
    print("成交额排行榜（前5名）:")
    for i, item in enumerate(sorted_by_amount[:5]):
        print(f"  {i+1}. {item['name']} ({item['code']}) {item['amount']:,} 元")
    print()

def demo_monitoring_mode():
    """演示监控模式"""
    print("=== 监控模式演示 ===\n")
    print("模拟实时监控（每5秒更新一次，共3次）...")
    
    spider = KaiPanLaLongHuSpider()
    
    for round_num in range(1, 4):
        print(f"\n--- 第 {round_num} 轮监控 ({datetime.now().strftime('%H:%M:%S')}) ---")
        
        # 生成新的模拟数据
        data = generate_mock_longhu_data(8)
        
        # 显示关键信息
        print("实时龙虎榜快报:")
        for i, item in enumerate(data[:3]):
            status = "📈" if item['change_percent'] > 0 else "📉" if item['change_percent'] < 0 else "➡️"
            print(f"  {status} {item['name']} {item['change_percent']:+5.1f}% "
                  f"成交额 {item['amount']/100000000:.1f}亿")
        
        # 保存数据
        spider.save_to_json(data, f'monitor_round_{round_num}')
        
        if round_num < 3:
            print("等待下次更新...")
            time.sleep(5)
    
    print("\n✓ 监控演示完成")

def main():
    """主演示程序"""
    print("开盘啦龙虎榜爬虫演示程序")
    print("=" * 50)
    print("注意：本演示使用模拟数据，实际使用需要配置真实API接口")
    print("=" * 50)
    print()
    
    try:
        # 基本功能演示
        data = demo_basic_functions()
        
        # 数据分析演示
        demo_data_analysis(data)
        
        # 询问是否演示监控模式
        choice = input("是否演示监控模式？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            demo_monitoring_mode()
        
        print("\n=== 演示完成 ===")
        print("生成的文件保存在 data/ 目录下")
        print("查看 '获取真实API接口指南.md' 了解如何获取真实接口")
        
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出错: {e}")

if __name__ == "__main__":
    main()
