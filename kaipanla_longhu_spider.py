#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦实时龙虎榜数据爬虫
基于TCP协议的真实数据获取
使用开盘啦的真实TCP接口：hwsockapp.longhuvip.com:14000
"""

import socket
import struct
import json
import time
import csv
from datetime import datetime
import logging
import os
import threading

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('longhu_spider.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class KaiPanLaLongHuSpider:
    def __init__(self):
        # 开盘啦TCP服务器配置
        self.tcp_host = "hwsockapp.longhuvip.com"
        self.tcp_port = 14000
        self.socket = None
        self.connected = False

        # API接口URL（用于构建TCP请求）
        self.api_url = "https://apphwhq.longhuvip.com/w1/api/index.php"

        # 创建数据目录
        if not os.path.exists('data'):
            os.makedirs('data')

    def connect(self):
        """连接到开盘啦TCP服务器"""
        try:
            logging.info(f"连接到开盘啦TCP服务器: {self.tcp_host}:{self.tcp_port}")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((self.tcp_host, self.tcp_port))
            self.connected = True
            logging.info("TCP连接成功")
            return True
        except Exception as e:
            logging.error(f"TCP连接失败: {str(e)}")
            self.connected = False
            return False

    def disconnect(self):
        """断开TCP连接"""
        if self.socket:
            try:
                self.socket.close()
                self.connected = False
                logging.info("TCP连接已断开")
            except:
                pass

    def build_tcp_packet(self, url):
        """构建TCP数据包"""
        try:
            # 根据抓包分析的协议格式构建数据包
            url_bytes = url.encode('utf-8')
            url_length = len(url_bytes)

            # 构建数据包头部
            # 格式：未知(2) + 长度(2) + 协议号(2) + 未知(2) + 未知(1) + URL长度(1) + URL
            packet = bytearray()
            packet.extend(b'\x20\x00')  # 未知字段
            packet.extend(struct.pack('<H', url_length + 3))  # 实际长度-3
            packet.extend(b'\xE6\x00')  # 协议号
            packet.extend(b'\x00\x00')  # 未知字段
            packet.extend(b'\x0A')      # 未知字段
            packet.extend(struct.pack('B', url_length))  # URL长度
            packet.extend(url_bytes)    # URL内容

            return bytes(packet)
        except Exception as e:
            logging.error(f"构建TCP数据包失败: {str(e)}")
            return None

    def send_tcp_request(self, api_params=""):
        """通过TCP发送请求"""
        try:
            if not self.connected:
                if not self.connect():
                    return None

            # 构建完整的API URL
            if api_params:
                full_url = f"{self.api_url}?{api_params}"
            else:
                full_url = self.api_url

            # 构建TCP数据包
            packet = self.build_tcp_packet(full_url)
            if not packet:
                return None

            # 发送数据包
            self.socket.send(packet)
            logging.info(f"发送TCP请求: {full_url}")

            # 接收响应
            response = self.receive_tcp_response()
            return response

        except Exception as e:
            logging.error(f"TCP请求失败: {str(e)}")
            self.connected = False
            return None

    def receive_tcp_response(self):
        """接收TCP响应"""
        try:
            # 接收响应头
            header = self.socket.recv(8)
            if len(header) < 8:
                return None

            # 解析响应长度
            response_length = struct.unpack('<H', header[2:4])[0]

            # 接收完整响应
            response_data = b''
            while len(response_data) < response_length:
                chunk = self.socket.recv(response_length - len(response_data))
                if not chunk:
                    break
                response_data += chunk

            # 尝试解析JSON
            try:
                response_text = response_data.decode('utf-8')
                return json.loads(response_text)
            except:
                # 如果不是JSON，返回原始数据
                return response_data.decode('utf-8', errors='ignore')

        except Exception as e:
            logging.error(f"接收TCP响应失败: {str(e)}")
            return None

    def _is_valid_longhu_data(self, data):
        """检查返回的数据是否是有效的龙虎榜数据"""
        if not data:
            return False

        data_str = str(data).lower()
        # 检查是否包含龙虎榜相关关键词
        keywords = ['龙虎榜', 'longhu', '涨停', '跌停', '股票', 'stock', '代码', 'code', '成交']
        return any(keyword in data_str for keyword in keywords)

    def _parse_longhu_data(self, data):
        """解析龙虎榜数据"""
        try:
            if isinstance(data, str):
                # 如果是字符串，尝试解析为JSON
                try:
                    data = json.loads(data)
                except:
                    return []

            if isinstance(data, dict):
                # 尝试不同的数据结构
                possible_keys = ['data', 'list', 'result', 'items', 'records']
                for key in possible_keys:
                    if key in data and isinstance(data[key], list):
                        return data[key]

                # 如果没有找到列表，检查是否整个dict就是一条记录
                if any(k in data for k in ['code', 'name', 'stock_code', 'stock_name']):
                    return [data]

            elif isinstance(data, list):
                return data

            return []

        except Exception as e:
            logging.error(f"解析龙虎榜数据失败: {str(e)}")
            return []
        
    def get_realtime_longhu(self):
        """获取实时龙虎榜数据"""
        try:
            logging.info("开始获取实时龙虎榜数据...")

            # 尝试多种可能的参数组合
            param_combinations = [
                "v=w21&c=FuPanLa&action=longhu&type=realtime",
                "v=w21&c=FuPanLa&method=getLongHu&type=today",
                "v=w21&c=FuPanLa&api=longhu&mode=realtime",
                "v=w21&c=FuPanLa&service=longhu",
                "v=w21&c=FuPanLa&longhu=1&real=1",
                "v=w21&c=FuPanLa&type=longhu",
                "v=w21&c=FuPanLa&action=longhu",
                "v=w21&c=FuPanLa&method=longhu",
                "v=w21&c=FuPanLa",  # 基础参数
            ]

            for params in param_combinations:
                logging.info(f"尝试参数组合: {params}")
                data = self.send_tcp_request(params)

                if data and self._is_valid_longhu_data(data):
                    longhu_data = self._parse_longhu_data(data)
                    if longhu_data:
                        logging.info(f"成功获取 {len(longhu_data)} 条实时龙虎榜数据")
                        return longhu_data

                # 避免请求过快
                time.sleep(1)

            logging.warning("所有参数组合都未能获取到有效数据")
            return []

        except Exception as e:
            logging.error(f"获取实时龙虎榜数据失败: {str(e)}")
            return []
        finally:
            # 确保断开连接
            self.disconnect()
    
    def get_daily_longhu(self, date=None):
        """获取指定日期的龙虎榜数据"""
        try:
            if date is None:
                date = datetime.now().strftime('%Y-%m-%d')

            logging.info(f"开始获取 {date} 的龙虎榜数据...")

            # 尝试多种日期格式和参数组合
            date_formats = [
                date,  # YYYY-MM-DD
                date.replace('-', ''),  # YYYYMMDD
                date.replace('-', '/'),  # YYYY/MM/DD
            ]

            param_templates = [
                "v=w21&c=FuPanLa&action=longhu&date={}&type=daily",
                "v=w21&c=FuPanLa&method=getLongHu&date={}&type=history",
                "v=w21&c=FuPanLa&api=longhu&date={}&mode=daily",
                "v=w21&c=FuPanLa&service=longhu&date={}",
                "v=w21&c=FuPanLa&longhu=1&date={}&daily=1",
                "v=w21&c=FuPanLa&type=longhu&date={}",
            ]

            for date_format in date_formats:
                for template in param_templates:
                    params = template.format(date_format)
                    logging.info(f"尝试参数组合: {params}")

                    data = self.send_tcp_request(params)

                    if data and self._is_valid_longhu_data(data):
                        longhu_data = self._parse_longhu_data(data)
                        if longhu_data:
                            logging.info(f"成功获取 {len(longhu_data)} 条 {date} 龙虎榜数据")
                            return longhu_data

                    time.sleep(1)

            logging.warning(f"未能获取到 {date} 的龙虎榜数据")
            return []

        except Exception as e:
            logging.error(f"获取日期龙虎榜数据失败: {str(e)}")
            return []
        finally:
            self.disconnect()
    
    def get_longhu_detail(self, stock_code):
        """获取指定股票的龙虎榜详情"""
        try:
            logging.info(f"开始获取股票 {stock_code} 的龙虎榜详情...")

            # 尝试多种股票代码格式
            code_formats = [
                stock_code,
                stock_code.zfill(6),  # 补齐到6位
                f"sz{stock_code}" if stock_code.startswith('0') or stock_code.startswith('3') else f"sh{stock_code}",
            ]

            param_templates = [
                "v=w21&c=FuPanLa&action=longhu_detail&code={}",
                "v=w21&c=FuPanLa&method=getLongHuDetail&code={}",
                "v=w21&c=FuPanLa&api=longhu&code={}&type=detail",
                "v=w21&c=FuPanLa&service=longhu&stock_code={}",
                "v=w21&c=FuPanLa&longhu=1&code={}&detail=1",
                "v=w21&c=FuPanLa&type=detail&code={}",
            ]

            for code_format in code_formats:
                for template in param_templates:
                    params = template.format(code_format)
                    logging.info(f"尝试参数组合: {params}")

                    data = self.send_tcp_request(params)

                    if data and self._is_valid_longhu_data(data):
                        detail_data = self._parse_longhu_data(data)
                        if detail_data:
                            logging.info(f"成功获取股票 {stock_code} 的龙虎榜详情")
                            return detail_data

                    time.sleep(1)

            logging.warning(f"未能获取到股票 {stock_code} 的龙虎榜详情")
            return {}

        except Exception as e:
            logging.error(f"获取龙虎榜详情失败: {str(e)}")
            return {}
        finally:
            self.disconnect()
    
    def save_to_csv(self, data, filename):
        """保存数据到CSV文件"""
        try:
            if not data:
                logging.warning("没有数据需要保存")
                return False

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            full_filename = f"data/{filename}_{timestamp}.csv"

            # 获取所有字段名
            fieldnames = set()
            for item in data:
                if isinstance(item, dict):
                    fieldnames.update(item.keys())
            fieldnames = list(fieldnames)

            with open(full_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for item in data:
                    if isinstance(item, dict):
                        writer.writerow(item)

            logging.info(f"数据已保存到: {full_filename}")
            return True

        except Exception as e:
            logging.error(f"保存CSV文件失败: {str(e)}")
            return False
    
    def save_to_json(self, data, filename):
        """保存数据到JSON文件"""
        try:
            if not data:
                logging.warning("没有数据需要保存")
                return False

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            full_filename = f"data/{filename}_{timestamp}.json"

            with open(full_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logging.info(f"数据已保存到: {full_filename}")
            return True

        except Exception as e:
            logging.error(f"保存JSON文件失败: {str(e)}")
            return False
    
    def run_realtime_monitor(self, interval=30):
        """运行实时监控模式"""
        logging.info(f"开始实时监控模式，间隔 {interval} 秒")
        
        try:
            while True:
                # 获取实时龙虎榜数据
                longhu_data = self.get_realtime_longhu()
                
                if longhu_data:
                    # 保存数据
                    self.save_to_csv(longhu_data, 'realtime_longhu')
                    self.save_to_json(longhu_data, 'realtime_longhu')
                    
                    # 打印简要信息
                    print(f"\n=== {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 实时龙虎榜 ===")
                    for item in longhu_data[:5]:  # 只显示前5条
                        print(f"股票: {item.get('name', 'N/A')} ({item.get('code', 'N/A')}) "
                              f"涨幅: {item.get('change_percent', 'N/A')}% "
                              f"成交额: {item.get('amount', 'N/A')}")
                
                # 等待下次获取
                time.sleep(interval)
                
        except KeyboardInterrupt:
            logging.info("用户中断，停止监控")
        except Exception as e:
            logging.error(f"监控过程中出错: {str(e)}")

def main():
    """主函数"""
    print("=== 开盘啦实时龙虎榜数据爬虫 ===")
    print("1. 获取实时龙虎榜")
    print("2. 获取今日龙虎榜")
    print("3. 获取指定日期龙虎榜")
    print("4. 实时监控模式")
    print("5. 退出")
    
    spider = KaiPanLaLongHuSpider()
    
    while True:
        try:
            choice = input("\n请选择功能 (1-5): ").strip()
            
            if choice == '1':
                data = spider.get_realtime_longhu()
                if data:
                    spider.save_to_csv(data, 'realtime_longhu')
                    print(f"成功获取 {len(data)} 条实时龙虎榜数据")
                    
            elif choice == '2':
                data = spider.get_daily_longhu()
                if data:
                    spider.save_to_csv(data, 'daily_longhu')
                    print(f"成功获取 {len(data)} 条今日龙虎榜数据")
                    
            elif choice == '3':
                date = input("请输入日期 (格式: YYYY-MM-DD): ").strip()
                data = spider.get_daily_longhu(date)
                if data:
                    spider.save_to_csv(data, f'longhu_{date}')
                    print(f"成功获取 {len(data)} 条 {date} 龙虎榜数据")
                    
            elif choice == '4':
                interval = input("请输入监控间隔秒数 (默认30): ").strip()
                interval = int(interval) if interval.isdigit() else 30
                spider.run_realtime_monitor(interval)
                
            elif choice == '5':
                print("退出程序")
                break
                
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n用户中断，退出程序")
            break
        except Exception as e:
            print(f"程序出错: {str(e)}")

if __name__ == "__main__":
    main()
