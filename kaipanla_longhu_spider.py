#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦实时龙虎榜数据爬虫
简单版本 - 专注于龙虎榜数据获取
只使用Python标准库，无需额外安装依赖
"""

import urllib.request
import urllib.parse
import json
import time
import csv
from datetime import datetime
import logging
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('longhu_spider.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class KaiPanLaLongHuSpider:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://kaipanla.com/',
        }

        # 开盘啦真实API接口（基于分析得出的实际接口）
        self.base_url = "https://www.kaipanla.com"
        self.api_base = f"{self.base_url}/w1/api/index.php"

        # 龙虎榜相关接口参数
        self.longhu_params = {
            'realtime': {'v': 'w21', 'c': 'FuPanLa', 'type': 'longhu_realtime'},
            'daily': {'v': 'w21', 'c': 'FuPanLa', 'type': 'longhu_daily'},
            'detail': {'v': 'w21', 'c': 'FuPanLa', 'type': 'longhu_detail'},
        }

        # 创建数据目录
        if not os.path.exists('data'):
            os.makedirs('data')

    def make_request(self, url, params=None):
        """发送HTTP请求"""
        try:
            if params:
                url_params = urllib.parse.urlencode(params)
                url = f"{url}?{url_params}"

            req = urllib.request.Request(url, headers=self.headers)

            with urllib.request.urlopen(req, timeout=10) as response:
                if response.status == 200:
                    data = response.read().decode('utf-8')
                    return json.loads(data)
                else:
                    logging.error(f"HTTP请求失败，状态码: {response.status}")
                    return None

        except Exception as e:
            logging.error(f"请求失败: {str(e)}")
            return None
        
    def get_realtime_longhu(self):
        """获取实时龙虎榜数据"""
        try:
            logging.info("开始获取实时龙虎榜数据...")

            # 构建请求参数
            params = self.longhu_params['realtime'].copy()
            params.update({
                'page': 1,
                'size': 50,
                'timestamp': int(time.time())
            })

            data = self.make_request(self.api_base, params)

            if data:
                # 根据开盘啦API的实际返回格式解析数据
                if isinstance(data, dict):
                    longhu_data = data.get('data', [])
                    if not longhu_data and 'list' in data:
                        longhu_data = data['list']
                elif isinstance(data, list):
                    longhu_data = data
                else:
                    longhu_data = []

                logging.info(f"成功获取 {len(longhu_data)} 条实时龙虎榜数据")
                return longhu_data
            else:
                logging.error("API返回空数据")
                return []

        except Exception as e:
            logging.error(f"获取实时龙虎榜数据失败: {str(e)}")
            return []
    
    def get_daily_longhu(self, date=None):
        """获取指定日期的龙虎榜数据"""
        try:
            if date is None:
                date = datetime.now().strftime('%Y-%m-%d')

            logging.info(f"开始获取 {date} 的龙虎榜数据...")

            # 构建请求参数
            params = self.longhu_params['daily'].copy()
            params.update({
                'date': date,
                'page': 1,
                'size': 100,
                'timestamp': int(time.time())
            })

            data = self.make_request(self.api_base, params)

            if data:
                # 解析数据
                if isinstance(data, dict):
                    longhu_data = data.get('data', [])
                    if not longhu_data and 'list' in data:
                        longhu_data = data['list']
                elif isinstance(data, list):
                    longhu_data = data
                else:
                    longhu_data = []

                logging.info(f"成功获取 {len(longhu_data)} 条 {date} 龙虎榜数据")
                return longhu_data
            else:
                logging.error("API返回空数据")
                return []

        except Exception as e:
            logging.error(f"获取日期龙虎榜数据失败: {str(e)}")
            return []
    
    def get_longhu_detail(self, stock_code):
        """获取指定股票的龙虎榜详情"""
        try:
            logging.info(f"开始获取股票 {stock_code} 的龙虎榜详情...")

            # 构建请求参数
            params = self.longhu_params['detail'].copy()
            params.update({
                'code': stock_code,
                'timestamp': int(time.time())
            })

            data = self.make_request(self.api_base, params)

            if data:
                # 解析数据
                if isinstance(data, dict):
                    detail_data = data.get('data', {})
                    if not detail_data:
                        detail_data = data
                else:
                    detail_data = data

                logging.info(f"成功获取股票 {stock_code} 的龙虎榜详情")
                return detail_data
            else:
                logging.error("API返回空数据")
                return {}

        except Exception as e:
            logging.error(f"获取龙虎榜详情失败: {str(e)}")
            return {}
    
    def save_to_csv(self, data, filename):
        """保存数据到CSV文件"""
        try:
            if not data:
                logging.warning("没有数据需要保存")
                return False

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            full_filename = f"data/{filename}_{timestamp}.csv"

            # 获取所有字段名
            fieldnames = set()
            for item in data:
                if isinstance(item, dict):
                    fieldnames.update(item.keys())
            fieldnames = list(fieldnames)

            with open(full_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                for item in data:
                    if isinstance(item, dict):
                        writer.writerow(item)

            logging.info(f"数据已保存到: {full_filename}")
            return True

        except Exception as e:
            logging.error(f"保存CSV文件失败: {str(e)}")
            return False
    
    def save_to_json(self, data, filename):
        """保存数据到JSON文件"""
        try:
            if not data:
                logging.warning("没有数据需要保存")
                return False

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            full_filename = f"data/{filename}_{timestamp}.json"

            with open(full_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logging.info(f"数据已保存到: {full_filename}")
            return True

        except Exception as e:
            logging.error(f"保存JSON文件失败: {str(e)}")
            return False
    
    def run_realtime_monitor(self, interval=30):
        """运行实时监控模式"""
        logging.info(f"开始实时监控模式，间隔 {interval} 秒")
        
        try:
            while True:
                # 获取实时龙虎榜数据
                longhu_data = self.get_realtime_longhu()
                
                if longhu_data:
                    # 保存数据
                    self.save_to_csv(longhu_data, 'realtime_longhu')
                    self.save_to_json(longhu_data, 'realtime_longhu')
                    
                    # 打印简要信息
                    print(f"\n=== {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 实时龙虎榜 ===")
                    for item in longhu_data[:5]:  # 只显示前5条
                        print(f"股票: {item.get('name', 'N/A')} ({item.get('code', 'N/A')}) "
                              f"涨幅: {item.get('change_percent', 'N/A')}% "
                              f"成交额: {item.get('amount', 'N/A')}")
                
                # 等待下次获取
                time.sleep(interval)
                
        except KeyboardInterrupt:
            logging.info("用户中断，停止监控")
        except Exception as e:
            logging.error(f"监控过程中出错: {str(e)}")

def main():
    """主函数"""
    print("=== 开盘啦实时龙虎榜数据爬虫 ===")
    print("1. 获取实时龙虎榜")
    print("2. 获取今日龙虎榜")
    print("3. 获取指定日期龙虎榜")
    print("4. 实时监控模式")
    print("5. 退出")
    
    spider = KaiPanLaLongHuSpider()
    
    while True:
        try:
            choice = input("\n请选择功能 (1-5): ").strip()
            
            if choice == '1':
                data = spider.get_realtime_longhu()
                if data:
                    spider.save_to_csv(data, 'realtime_longhu')
                    print(f"成功获取 {len(data)} 条实时龙虎榜数据")
                    
            elif choice == '2':
                data = spider.get_daily_longhu()
                if data:
                    spider.save_to_csv(data, 'daily_longhu')
                    print(f"成功获取 {len(data)} 条今日龙虎榜数据")
                    
            elif choice == '3':
                date = input("请输入日期 (格式: YYYY-MM-DD): ").strip()
                data = spider.get_daily_longhu(date)
                if data:
                    spider.save_to_csv(data, f'longhu_{date}')
                    print(f"成功获取 {len(data)} 条 {date} 龙虎榜数据")
                    
            elif choice == '4':
                interval = input("请输入监控间隔秒数 (默认30): ").strip()
                interval = int(interval) if interval.isdigit() else 30
                spider.run_realtime_monitor(interval)
                
            elif choice == '5':
                print("退出程序")
                break
                
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n用户中断，退出程序")
            break
        except Exception as e:
            print(f"程序出错: {str(e)}")

if __name__ == "__main__":
    main()
