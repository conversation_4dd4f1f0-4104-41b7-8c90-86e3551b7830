#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦API接口测试器
测试真实的开盘啦API接口
"""

import urllib.request
import urllib.parse
import json
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_kaipanla_api():
    """测试开盘啦API接口"""
    
    # 开盘啦API配置
    api_url = "https://www.kaipanla.com/w1/api/index.php"
    
    headers = {
        'User-Agent': 'kaipanla/6.8.0 (iPhone; iOS 15.0; Scale/3.00)',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'X-Requested-With': 'XMLHttpRequest',
    }
    
    # 基础参数
    base_params = {
        'v': 'w21',
        'c': 'FuPanLa'
    }
    
    # 测试参数组合
    test_cases = [
        # 龙虎榜相关
        {'action': 'longhu'},
        {'method': 'longhu'},
        {'type': 'longhu'},
        {'api': 'longhu'},
        {'service': 'longhu'},
        {'longhu': '1'},
        
        # 实时数据
        {'action': 'realtime'},
        {'type': 'realtime'},
        {'real': '1'},
        
        # 涨停板
        {'action': 'ztb'},
        {'type': 'ztb'},
        {'ztb': '1'},
        
        # 基础信息
        {'action': 'info'},
        {'method': 'getInfo'},
        {},  # 只使用基础参数
    ]
    
    print("=== 开盘啦API接口测试 ===")
    print(f"测试API地址: {api_url}")
    print(f"基础参数: {base_params}")
    print()
    
    successful_apis = []
    
    for i, test_params in enumerate(test_cases):
        print(f"测试 {i+1}/{len(test_cases)}: {test_params}")
        
        # 合并参数
        params = base_params.copy()
        params.update(test_params)
        
        try:
            # 发送POST请求
            post_data = urllib.parse.urlencode(params).encode('utf-8')
            req = urllib.request.Request(api_url, data=post_data, headers=headers)
            
            with urllib.request.urlopen(req, timeout=10) as response:
                if response.status == 200:
                    content = response.read().decode('utf-8')
                    
                    # 检查返回内容
                    if content and len(content) > 10:
                        print(f"  ✓ 成功获取数据 (长度: {len(content)})")
                        print(f"  预览: {content[:100]}...")
                        
                        # 检查是否包含股票相关关键词
                        keywords = ['股票', '代码', '涨停', '龙虎榜', 'stock', 'code', 'longhu']
                        found_keywords = [k for k in keywords if k in content.lower()]
                        
                        if found_keywords:
                            print(f"  🎯 发现相关关键词: {found_keywords}")
                            successful_apis.append({
                                'params': params,
                                'content': content,
                                'keywords': found_keywords
                            })
                        
                        # 尝试解析JSON
                        try:
                            json_data = json.loads(content)
                            print(f"  📊 JSON数据结构: {list(json_data.keys()) if isinstance(json_data, dict) else type(json_data)}")
                        except:
                            print("  📝 非JSON格式数据")
                    else:
                        print(f"  ✗ 返回数据为空或过短")
                else:
                    print(f"  ✗ HTTP错误: {response.status}")
                    
        except Exception as e:
            print(f"  ✗ 请求失败: {str(e)}")
        
        print()
        time.sleep(1)  # 避免请求过快
    
    # 汇总结果
    print("=== 测试结果汇总 ===")
    if successful_apis:
        print(f"发现 {len(successful_apis)} 个可能有效的API接口:")
        for i, api in enumerate(successful_apis):
            print(f"{i+1}. 参数: {api['params']}")
            print(f"   关键词: {api['keywords']}")
            print(f"   数据预览: {api['content'][:150]}...")
            print()
    else:
        print("未发现有效的API接口")
        print("可能的原因:")
        print("1. API接口已变更")
        print("2. 需要特殊的认证参数")
        print("3. 请求头不正确")
        print("4. 参数组合不对")

def test_alternative_endpoints():
    """测试其他可能的API端点"""
    print("\n=== 测试其他可能的API端点 ===")
    
    alternative_urls = [
        "https://api.kaipanla.com/v1/longhu",
        "https://app.kaipanla.com/api/longhu",
        "https://m.kaipanla.com/api/longhu",
        "https://www.kaipanla.com/api/longhu",
        "https://www.kaipanla.com/longhu/api",
    ]
    
    headers = {
        'User-Agent': 'kaipanla/6.8.0 (iPhone; iOS 15.0; Scale/3.00)',
        'Accept': 'application/json',
    }
    
    for url in alternative_urls:
        print(f"测试: {url}")
        try:
            req = urllib.request.Request(url, headers=headers)
            with urllib.request.urlopen(req, timeout=5) as response:
                if response.status == 200:
                    content = response.read().decode('utf-8')
                    print(f"  ✓ 可访问 (长度: {len(content)})")
                    print(f"  预览: {content[:100]}...")
                else:
                    print(f"  ✗ HTTP错误: {response.status}")
        except Exception as e:
            print(f"  ✗ 无法访问: {str(e)}")
        print()

def save_test_results(successful_apis):
    """保存测试结果"""
    if successful_apis:
        with open('api_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(successful_apis, f, ensure_ascii=False, indent=2)
        print("测试结果已保存到 api_test_results.json")

if __name__ == "__main__":
    # 测试主API
    test_kaipanla_api()
    
    # 测试其他端点
    test_alternative_endpoints()
    
    print("\n=== 测试完成 ===")
    print("如果发现有效接口，请查看详细输出和保存的结果文件")
