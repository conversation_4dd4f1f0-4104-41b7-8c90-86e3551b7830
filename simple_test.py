#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试开盘啦龙虎榜爬虫
"""

from kaipanla_longhu_spider import KaiPanLaLongHuSpider
import json

def main():
    print("=== 开盘啦龙虎榜爬虫测试 ===")
    print("正在初始化爬虫...")
    
    # 创建爬虫实例
    spider = KaiPanLaLongHuSpider()
    
    print("1. 测试TCP连接...")
    if spider.connect():
        print("✓ TCP连接成功")
        spider.disconnect()
    else:
        print("✗ TCP连接失败")
        return
    
    print("\n2. 测试获取实时龙虎榜数据...")
    try:
        data = spider.get_realtime_longhu()
        if data:
            print(f"✓ 成功获取 {len(data)} 条数据")
            
            # 显示前几条数据
            for i, item in enumerate(data[:3]):
                print(f"  {i+1}. {item}")
            
            # 保存数据
            spider.save_to_csv(data, 'test_realtime')
            spider.save_to_json(data, 'test_realtime')
            print("✓ 数据已保存")
        else:
            print("✗ 未获取到数据")
    except Exception as e:
        print(f"✗ 获取数据失败: {e}")
    
    print("\n3. 测试获取今日龙虎榜数据...")
    try:
        data = spider.get_daily_longhu()
        if data:
            print(f"✓ 成功获取 {len(data)} 条今日数据")
            spider.save_to_csv(data, 'test_daily')
        else:
            print("✗ 未获取到今日数据")
    except Exception as e:
        print(f"✗ 获取今日数据失败: {e}")
    
    print("\n=== 测试完成 ===")
    print("如果成功获取数据，文件已保存到 data/ 目录")

if __name__ == "__main__":
    main()
