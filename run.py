#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦龙虎榜爬虫启动脚本
基于TCP协议的真实数据获取
"""

import os
import sys
import socket

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 6):
        print("✗ 需要Python 3.6或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✓ Python版本检查通过: {sys.version.split()[0]}")
    return True

def check_network():
    """检查网络连接"""
    try:
        # 测试连接到开盘啦TCP服务器
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect(("hwsockapp.longhuvip.com", 14000))
        sock.close()
        print("✓ 网络连接正常")
        return True
    except Exception as e:
        print(f"✗ 网络连接失败: {e}")
        print("请检查网络连接和防火墙设置")
        return False

def create_data_dir():
    """创建数据目录"""
    data_dir = './data'
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
        print(f"✓ 创建数据目录: {data_dir}")
    else:
        print(f"✓ 数据目录已存在: {data_dir}")

def show_menu():
    """显示菜单"""
    print("\n" + "="*50)
    print("开盘啦龙虎榜数据爬虫")
    print("基于TCP协议 - 获取真实数据")
    print("="*50)
    print("1. 运行主程序（交互式菜单）")
    print("2. 运行完整演示")
    print("3. 简单测试")
    print("4. TCP连接测试")
    print("5. 退出")
    print("="*50)

def main():
    """主函数"""
    print("开盘啦龙虎榜数据爬虫启动器")
    print("正在初始化...")

    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        sys.exit(1)

    # 检查网络连接
    if not check_network():
        print("⚠️  网络连接有问题，但仍可以尝试运行程序")

    # 创建数据目录
    create_data_dir()

    while True:
        try:
            show_menu()
            choice = input("请选择功能 (1-5): ").strip()

            if choice == '1':
                print("\n启动主程序...")
                from kaipanla_longhu_spider import main as spider_main
                spider_main()

            elif choice == '2':
                print("\n启动完整演示...")
                import 完整示例
                完整示例.main()

            elif choice == '3':
                print("\n启动简单测试...")
                import simple_test
                simple_test.main()

            elif choice == '4':
                print("\n启动TCP连接测试...")
                import test_tcp_connection
                test_tcp_connection.main()

            elif choice == '5':
                print("退出程序")
                break

            else:
                print("无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n\n用户中断，退出程序")
            break
        except ImportError as e:
            print(f"✗ 导入模块失败: {e}")
            print("请确保所有文件都在同一目录下")
        except Exception as e:
            print(f"程序运行出错: {e}")
            print("请检查错误信息并重试")

        input("\n按回车键继续...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"启动器运行出错: {e}")
        input("按回车键退出...")
