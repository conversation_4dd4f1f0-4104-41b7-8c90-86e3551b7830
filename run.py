#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦龙虎榜爬虫启动脚本
"""

import os
import sys
from kaipanla_longhu_spider import main

def check_dependencies():
    """检查依赖包"""
    try:
        import requests
        import pandas
        print("✓ 依赖包检查通过")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖包: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def create_data_dir():
    """创建数据目录"""
    data_dir = './data'
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
        print(f"✓ 创建数据目录: {data_dir}")

if __name__ == "__main__":
    print("=== 开盘啦龙虎榜数据爬虫 ===")
    print("正在初始化...")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建数据目录
    create_data_dir()
    
    # 启动主程序
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序已退出")
    except Exception as e:
        print(f"程序运行出错: {e}")
        input("按回车键退出...")
