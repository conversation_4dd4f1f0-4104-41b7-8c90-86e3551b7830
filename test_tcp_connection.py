#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试开盘啦TCP连接
验证TCP协议是否可用
"""

import socket
import struct
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_tcp_connection():
    """测试TCP连接"""
    host = "hwsockapp.longhuvip.com"
    port = 14000
    
    print(f"=== 测试开盘啦TCP连接 ===")
    print(f"服务器: {host}:{port}")
    print()
    
    try:
        # 创建socket连接
        print("1. 创建TCP连接...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        # 连接服务器
        print("2. 连接服务器...")
        sock.connect((host, port))
        print("✓ TCP连接成功")
        
        # 构建测试数据包
        print("3. 构建测试数据包...")
        test_url = "https://apphwhq.longhuvip.com/w1/api/index.php"
        url_bytes = test_url.encode('utf-8')
        url_length = len(url_bytes)
        
        # 根据抓包分析构建数据包
        packet = bytearray()
        packet.extend(b'\x20\x00')  # 未知字段
        packet.extend(struct.pack('<H', url_length + 3))  # 实际长度-3
        packet.extend(b'\xE6\x00')  # 协议号
        packet.extend(b'\x00\x00')  # 未知字段
        packet.extend(b'\x0A')      # 未知字段
        packet.extend(struct.pack('B', url_length))  # URL长度
        packet.extend(url_bytes)    # URL内容
        
        print(f"✓ 数据包构建完成，长度: {len(packet)} 字节")
        print(f"数据包内容: {packet.hex()}")
        
        # 发送数据包
        print("4. 发送数据包...")
        sock.send(bytes(packet))
        print("✓ 数据包发送成功")
        
        # 接收响应
        print("5. 接收响应...")
        try:
            # 设置较短的超时时间
            sock.settimeout(5)
            response = sock.recv(1024)
            
            if response:
                print(f"✓ 收到响应，长度: {len(response)} 字节")
                print(f"响应内容(hex): {response.hex()}")
                print(f"响应内容(text): {response.decode('utf-8', errors='ignore')}")
            else:
                print("✗ 未收到响应")
                
        except socket.timeout:
            print("⚠ 接收响应超时（这可能是正常的）")
        except Exception as e:
            print(f"✗ 接收响应失败: {e}")
        
        # 关闭连接
        sock.close()
        print("6. 连接已关闭")
        
        return True
        
    except socket.timeout:
        print("✗ 连接超时")
        return False
    except ConnectionRefusedError:
        print("✗ 连接被拒绝")
        return False
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False

def test_different_packets():
    """测试不同的数据包格式"""
    print("\n=== 测试不同的数据包格式 ===")
    
    host = "hwsockapp.longhuvip.com"
    port = 14000
    
    # 不同的测试URL
    test_urls = [
        "https://apphwhq.longhuvip.com/w1/api/index.php",
        "https://apphwhq.longhuvip.com/w1/api/index.php?v=w21&c=FuPanLa",
        "https://apphwhq.longhuvip.com/w1/api/index.php?v=w21&c=FuPanLa&action=longhu",
    ]
    
    for i, url in enumerate(test_urls):
        print(f"\n测试 {i+1}: {url}")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((host, port))
            
            # 构建数据包
            url_bytes = url.encode('utf-8')
            url_length = len(url_bytes)
            
            packet = bytearray()
            packet.extend(b'\x20\x00')
            packet.extend(struct.pack('<H', url_length + 3))
            packet.extend(b'\xE6\x00')
            packet.extend(b'\x00\x00')
            packet.extend(b'\x0A')
            packet.extend(struct.pack('B', url_length))
            packet.extend(url_bytes)
            
            # 发送并接收
            sock.send(bytes(packet))
            
            try:
                response = sock.recv(1024)
                if response:
                    print(f"  ✓ 收到响应: {len(response)} 字节")
                    # 尝试解析响应
                    try:
                        text = response.decode('utf-8')
                        if 'json' in text.lower() or '{' in text:
                            print(f"  📊 可能的JSON响应: {text[:100]}...")
                    except:
                        pass
                else:
                    print("  ✗ 无响应")
            except socket.timeout:
                print("  ⚠ 响应超时")
            
            sock.close()
            time.sleep(1)  # 避免请求过快
            
        except Exception as e:
            print(f"  ✗ 测试失败: {e}")

def main():
    """主函数"""
    print("开盘啦TCP连接测试工具")
    print("=" * 50)
    
    # 基础连接测试
    success = test_tcp_connection()
    
    if success:
        # 测试不同数据包
        test_different_packets()
        
        print("\n=== 测试结果 ===")
        print("✓ TCP连接正常")
        print("✓ 数据包发送成功")
        print("⚠ 如果没有收到有效响应，可能需要：")
        print("  1. 调整数据包格式")
        print("  2. 添加认证信息")
        print("  3. 使用正确的协议参数")
    else:
        print("\n=== 测试结果 ===")
        print("✗ TCP连接失败")
        print("可能的原因：")
        print("  1. 服务器地址或端口错误")
        print("  2. 网络连接问题")
        print("  3. 服务器不可用")
        print("  4. 防火墙阻止连接")

if __name__ == "__main__":
    main()
