#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘啦龙虎榜爬虫配置文件
"""

# API配置
API_CONFIG = {
    'base_url': 'https://api.kaipanla.com',
    'timeout': 10,
    'retry_times': 3,
    'retry_delay': 1,
}

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Referer': 'https://kaipanla.com/',
}

# 数据保存配置
SAVE_CONFIG = {
    'save_csv': True,
    'save_json': True,
    'save_excel': False,
    'data_dir': './data',
}

# 监控配置
MONITOR_CONFIG = {
    'default_interval': 30,  # 默认监控间隔（秒）
    'max_records': 1000,     # 最大记录数
    'auto_save': True,       # 自动保存
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'longhu_spider.log',
    'encoding': 'utf-8',
}
