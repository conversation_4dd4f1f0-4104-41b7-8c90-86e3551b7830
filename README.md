# 开盘啦实时龙虎榜数据爬虫

一个简单高效的开盘啦实时龙虎榜数据爬取工具，使用Python标准库开发，无需额外安装依赖。

## 🚀 功能特点

- ✅ 获取实时龙虎榜数据
- ✅ 获取历史龙虎榜数据
- ✅ 支持指定日期查询
- ✅ 实时监控模式
- ✅ 多种数据格式输出（CSV、JSON）
- ✅ 完整的日志记录
- ✅ 简单易用的命令行界面
- ✅ 纯Python标准库，无需额外依赖
- ✅ 包含完整的API接口获取指南

## 📁 项目文件

```
kaipanla/
├── kaipanla_longhu_spider.py    # 主爬虫程序
├── config.py                    # 配置文件
├── run.py                      # 启动脚本
├── run.bat                     # Windows批处理文件
├── demo.py                     # 功能演示程序
├── test_spider.py              # 测试脚本
├── requirements.txt            # 依赖说明
├── README.md                   # 项目说明
├── 使用说明.md                  # 详细使用说明
├── 获取真实API接口指南.md        # API接口获取指南
└── data/                       # 数据输出目录
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.6+ （推荐3.8+）
- 网络连接
- **无需安装额外依赖包**

### 2. 运行演示程序

```bash
# 运行功能演示（使用模拟数据）
python demo.py
```

### 3. 运行主程序

**Windows用户：**
```bash
# 双击运行
run.bat

# 或命令行运行
python run.py
```

**Linux/Mac用户：**
```bash
python run.py
```

### 4. 测试功能

```bash
python test_spider.py
```

## ⚠️ 重要说明

**当前代码中的API接口是示例接口，需要您自己获取真实的开盘啦API接口。**

请查看 `获取真实API接口指南.md` 了解如何获取真实接口。

## 📖 使用说明

### 演示模式（推荐新手）

```bash
python demo.py
```

演示程序使用模拟数据展示所有功能，无需配置API接口。

### 正式使用

程序启动后会显示菜单：

```
=== 开盘啦实时龙虎榜数据爬虫 ===
1. 获取实时龙虎榜
2. 获取今日龙虎榜
3. 获取指定日期龙虎榜
4. 实时监控模式
5. 退出
```

### 功能说明

1. **获取实时龙虎榜** - 获取当前最新的龙虎榜数据
2. **获取今日龙虎榜** - 获取今天的龙虎榜汇总数据
3. **获取指定日期龙虎榜** - 查询历史某一天的龙虎榜数据
4. **实时监控模式** - 持续监控并自动保存数据

## 📊 数据输出

数据会自动保存到 `data/` 目录：

- `data/realtime_longhu_YYYYMMDD_HHMMSS.csv` - 实时龙虎榜CSV格式
- `data/realtime_longhu_YYYYMMDD_HHMMSS.json` - 实时龙虎榜JSON格式
- `data/daily_longhu_YYYYMMDD_HHMMSS.csv` - 日度龙虎榜CSV格式
- `longhu_spider.log` - 程序运行日志

## 📋 数据字段说明

龙虎榜数据包含以下主要字段：

- `code` - 股票代码
- `name` - 股票名称
- `price` - 当前价格
- `change_percent` - 涨跌幅
- `amount` - 成交金额
- `volume` - 成交量
- `reason` - 上榜原因
- `date` - 日期
- `time` - 时间

## 🔧 获取真实API接口

要使用真实数据，需要获取开盘啦的真实API接口：

1. **查看指南** - 阅读 `获取真实API接口指南.md`
2. **抓包工具** - 使用Fiddler或Charles抓取APP请求
3. **更新代码** - 将真实接口更新到代码中

### 快速抓包步骤

```bash
1. 安装Fiddler
2. 配置手机代理
3. 打开开盘啦APP
4. 查看龙虎榜页面
5. 分析网络请求
6. 更新代码中的API接口
```

## ⚙️ 配置说明

可以通过修改 `config.py` 文件来调整：

- API接口地址
- 请求头信息
- 数据保存格式
- 监控参数
- 日志配置

## 🔍 故障排除

### 常见问题

1. **无法获取数据**
   - 检查API接口是否正确
   - 查看 `longhu_spider.log` 日志文件
   - 确认网络连接正常

2. **SSL证书错误**
   - 可能需要更新Python版本
   - 或使用requests库替代urllib

3. **数据格式错误**
   - 检查API返回的数据结构
   - 更新代码中的数据解析逻辑

### 调试方法

```bash
# 查看日志
cat longhu_spider.log

# 运行测试
python test_spider.py

# 运行演示
python demo.py
```

## 📚 文档说明

- `README.md` - 项目概述和快速开始
- `使用说明.md` - 详细使用说明
- `获取真实API接口指南.md` - API接口获取教程

## 🔄 更新日志

### v1.0.0 (2024-01-15)
- ✅ 初始版本发布
- ✅ 支持实时龙虎榜数据获取
- ✅ 支持历史数据查询
- ✅ 实时监控功能
- ✅ 使用Python标准库，无需额外依赖
- ✅ 包含完整的API接口获取指南
- ✅ 提供功能演示程序

## ⚖️ 免责声明

本工具仅供学习和研究使用，使用者需要：

1. 遵守相关法律法规
2. 尊重网站服务条款
3. 合理使用，避免对服务器造成压力
4. 投资有风险，数据仅供参考
5. 不得用于商业用途

## 📞 获取帮助

如果遇到问题：

1. 查看项目文档
2. 检查日志文件
3. 运行测试程序
4. 提交Issue反馈

---

**⚠️ 重要提醒：请合法合规使用本工具，仅用于学习研究目的！**
