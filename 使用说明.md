# 开盘啦实时龙虎榜数据爬虫使用说明

## 项目简介

这是一个专门用于爬取开盘啦实时龙虎榜数据的Python爬虫工具。项目采用简洁设计，只使用Python标准库，无需安装额外依赖。

## 文件说明

- `kaipanla_longhu_spider.py` - 主要爬虫程序
- `config.py` - 配置文件
- `run.py` - 启动脚本
- `run.bat` - Windows批处理启动文件
- `test_spider.py` - 测试脚本
- `requirements.txt` - 依赖说明（本项目无需额外依赖）
- `README.md` - 项目说明
- `使用说明.md` - 详细使用说明

## 快速开始

### 1. 环境要求

- Python 3.6+ （推荐3.8+）
- 网络连接

### 2. 运行程序

**方法一：直接运行主程序**
```bash
python kaipanla_longhu_spider.py
```

**方法二：使用启动脚本**
```bash
python run.py
```

**方法三：Windows用户双击运行**
```
双击 run.bat 文件
```

### 3. 测试功能

```bash
python test_spider.py
```

## 功能说明

### 主要功能

1. **获取实时龙虎榜** - 获取当前最新的龙虎榜数据
2. **获取今日龙虎榜** - 获取今天的龙虎榜汇总数据
3. **获取指定日期龙虎榜** - 查询历史某一天的龙虎榜数据
4. **实时监控模式** - 持续监控并自动保存数据
5. **数据保存** - 支持CSV和JSON格式

### 数据字段

龙虎榜数据包含以下字段：
- `code` - 股票代码
- `name` - 股票名称
- `price` - 当前价格
- `change_percent` - 涨跌幅
- `amount` - 成交金额
- `volume` - 成交量
- `reason` - 上榜原因
- `date` - 日期

## 重要说明

### ✅ 真实TCP协议接口

**本项目已集成开盘啦的真实TCP协议接口，可以直接获取真实数据！**

#### TCP连接信息
- **服务器地址**: `hwsockapp.longhuvip.com`
- **端口**: `14000`
- **协议**: TCP Socket
- **数据格式**: 基于开盘啦官方协议

#### 协议特点
1. **真实数据源** - 直接连接开盘啦官方TCP服务器
2. **实时性强** - TCP连接，数据更新及时
3. **稳定可靠** - 基于官方协议，不易失效
4. **无需抓包** - 已经解析好协议格式

#### 数据包格式
```
数据包结构：
- 头部标识: 0x20 0x00
- 数据长度: 2字节 (小端序)
- 协议号: 0xE6 0x00
- 未知字段: 0x00 0x00 0x0A
- URL长度: 1字节
- URL内容: 变长
```

### 抓包步骤详解

1. **安装抓包工具**
   - Fiddler（Windows推荐）
   - Charles（跨平台）

2. **配置代理**
   - 设置手机/电脑代理到抓包工具
   - 安装SSL证书（如果是HTTPS）

3. **抓取请求**
   - 打开开盘啦APP
   - 进入龙虎榜页面
   - 刷新数据，观察网络请求
   - 记录API接口地址、请求参数、请求头

4. **分析数据格式**
   - 查看返回的JSON数据结构
   - 确认数据字段名称
   - 了解分页参数

## 使用示例

### 基本使用

```python
from kaipanla_longhu_spider import KaiPanLaLongHuSpider

# 创建爬虫实例
spider = KaiPanLaLongHuSpider()

# 获取实时龙虎榜
data = spider.get_realtime_longhu()

# 保存数据
spider.save_to_csv(data, 'longhu_data')
spider.save_to_json(data, 'longhu_data')
```

### 监控模式

```python
# 每30秒获取一次数据
spider.run_realtime_monitor(interval=30)
```

## 数据输出

数据会保存在 `data/` 目录下：

- `data/realtime_longhu_20240115_143022.csv`
- `data/realtime_longhu_20240115_143022.json`
- `data/daily_longhu_20240115_143022.csv`

## 注意事项

1. **合规使用**
   - 遵守相关法律法规
   - 尊重网站服务条款
   - 合理控制请求频率

2. **技术要求**
   - 需要一定的网络抓包知识
   - 可能需要分析JavaScript加密
   - API接口可能会变化

3. **数据准确性**
   - 数据仅供参考
   - 投资决策请以官方数据为准
   - 及时验证数据有效性

## 故障排除

### 常见问题

1. **无法获取数据**
   - 检查API接口是否正确
   - 确认网络连接正常
   - 查看日志文件了解详细错误

2. **数据格式错误**
   - 检查API返回的数据结构
   - 更新代码中的字段解析逻辑

3. **请求被拒绝**
   - 可能需要添加认证信息
   - 检查请求头是否完整
   - 控制请求频率

### 调试方法

1. **查看日志**
   ```
   tail -f longhu_spider.log
   ```

2. **测试API接口**
   ```bash
   python test_spider.py
   ```

3. **手动测试请求**
   ```python
   import urllib.request
   import json
   
   url = "你的API接口"
   req = urllib.request.Request(url)
   response = urllib.request.urlopen(req)
   data = json.loads(response.read().decode('utf-8'))
   print(data)
   ```

## 扩展功能

### 添加新的数据源

1. 在 `longhu_urls` 中添加新的API接口
2. 创建对应的获取方法
3. 更新数据保存逻辑

### 添加数据库支持

```python
import sqlite3

def save_to_database(self, data):
    conn = sqlite3.connect('longhu.db')
    # 实现数据库保存逻辑
    conn.close()
```

### 添加邮件通知

```python
import smtplib
from email.mime.text import MIMEText

def send_notification(self, data):
    # 实现邮件通知逻辑
    pass
```

## 免责声明

本工具仅供学习和研究使用，使用者需要：

1. 遵守相关法律法规
2. 尊重网站服务条款  
3. 合理使用，避免对服务器造成压力
4. 投资有风险，数据仅供参考

---

**如有问题，请查看日志文件或联系开发者。**
