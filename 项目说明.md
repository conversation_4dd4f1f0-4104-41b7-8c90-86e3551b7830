# 开盘啦实时龙虎榜数据爬虫

## 🎯 项目概述

这是一个专门用于获取开盘啦实时龙虎榜数据的Python爬虫工具。项目基于开盘啦的真实TCP协议接口，可以直接获取真实的龙虎榜数据，无需复杂的抓包和逆向工程。

## ✨ 核心特点

### 🔥 真实数据源
- **TCP协议**: 使用开盘啦官方TCP接口 `hwsockapp.longhuvip.com:14000`
- **实时数据**: 直接从开盘啦服务器获取最新龙虎榜数据
- **稳定可靠**: 基于官方协议，不易失效

### 🚀 技术优势
- **纯Python**: 只使用Python标准库，无需额外依赖
- **协议解析**: 已完成TCP协议的逆向解析
- **自动重连**: 支持连接断开后自动重连
- **多格式输出**: 支持CSV、JSON等多种数据格式

### 📊 功能完整
- **实时龙虎榜**: 获取当前最新的龙虎榜数据
- **历史数据**: 支持查询指定日期的龙虎榜
- **股票详情**: 获取单个股票的龙虎榜详细信息
- **监控模式**: 支持实时监控和自动保存

## 📁 文件结构

```
kaipanla/
├── kaipanla_longhu_spider.py    # 主爬虫程序（核心）
├── run.py                       # 启动器（推荐使用）
├── 完整示例.py                   # 完整功能演示
├── simple_test.py               # 简单测试
├── test_tcp_connection.py       # TCP连接测试
├── config.py                    # 配置文件
├── README.md                    # 项目说明
├── 使用说明.md                   # 详细使用说明
├── 项目说明.md                   # 本文件
├── run.bat                      # Windows批处理文件
└── data/                        # 数据输出目录
```

## 🚀 快速开始

### 1. 环境要求
- Python 3.6+ （推荐3.8+）
- 网络连接（能访问开盘啦服务器）

### 2. 运行方式

**方式一：使用启动器（推荐）**
```bash
python run.py
```

**方式二：直接运行主程序**
```bash
python kaipanla_longhu_spider.py
```

**方式三：运行完整演示**
```bash
python 完整示例.py
```

**方式四：Windows用户**
```bash
双击 run.bat
```

### 3. 测试连接
```bash
python test_tcp_connection.py
```

## 🔧 核心技术

### TCP协议解析

开盘啦使用自定义的TCP协议，数据包格式如下：

```
数据包结构：
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ 头部标识    │ 数据长度    │ 协议号      │ 控制字段    │
│ 0x20 0x00  │ 2字节(LE)   │ 0xE6 0x00  │ 0x00 0x00  │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ 标识字段    │ URL长度     │ URL内容     │             │
│ 0x0A        │ 1字节       │ 变长        │             │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

### API接口

基础API地址：`https://apphwhq.longhuvip.com/w1/api/index.php`

常用参数组合：
- `v=w21&c=FuPanLa` - 基础参数
- `action=longhu` - 龙虎榜数据
- `type=realtime` - 实时数据
- `date=YYYY-MM-DD` - 指定日期

## 📊 数据格式

### 龙虎榜数据字段

```json
{
  "code": "000001",           // 股票代码
  "name": "平安银行",          // 股票名称
  "price": 12.50,            // 当前价格
  "change_percent": 5.2,     // 涨跌幅
  "amount": 1500000000,      // 成交金额
  "volume": 120000000,       // 成交量
  "reason": "日涨幅偏离值达7%", // 上榜原因
  "date": "2024-01-15",      // 日期
  "time": "14:30:00"         // 时间
}
```

### 输出文件

数据会自动保存到 `data/` 目录：

- `realtime_longhu_20240115_143022.csv` - 实时龙虎榜CSV
- `realtime_longhu_20240115_143022.json` - 实时龙虎榜JSON
- `daily_longhu_20240115_143022.csv` - 日度龙虎榜CSV

## 🔍 使用示例

### 基础使用

```python
from kaipanla_longhu_spider import KaiPanLaLongHuSpider

# 创建爬虫实例
spider = KaiPanLaLongHuSpider()

# 获取实时龙虎榜
data = spider.get_realtime_longhu()

# 保存数据
spider.save_to_csv(data, 'longhu_data')
spider.save_to_json(data, 'longhu_data')
```

### 监控模式

```python
# 实时监控（每30秒获取一次）
spider.run_realtime_monitor(interval=30)
```

### 获取历史数据

```python
# 获取指定日期的龙虎榜
data = spider.get_daily_longhu('2024-01-15')
```

## ⚠️ 注意事项

### 合规使用
1. **仅供学习研究** - 请勿用于商业用途
2. **遵守法律法规** - 遵守相关法律法规和网站条款
3. **合理频率** - 避免过于频繁的请求
4. **数据仅供参考** - 投资决策请以官方数据为准

### 技术注意
1. **网络环境** - 需要稳定的网络连接
2. **防火墙** - 确保TCP端口14000未被阻止
3. **协议变更** - 如果协议变更，可能需要更新代码
4. **错误处理** - 程序已包含完整的错误处理机制

## 🔧 故障排除

### 常见问题

**1. TCP连接失败**
```
原因：网络问题或服务器不可用
解决：检查网络连接，尝试使用VPN
```

**2. 无法获取数据**
```
原因：协议参数可能需要调整
解决：查看日志文件，尝试不同参数组合
```

**3. 数据格式错误**
```
原因：API返回格式变化
解决：更新数据解析逻辑
```

### 调试方法

```bash
# 查看日志
cat longhu_spider.log

# 测试TCP连接
python test_tcp_connection.py

# 运行简单测试
python simple_test.py
```

## 🔄 更新日志

### v1.0.0 (2024-01-15)
- ✅ 集成开盘啦真实TCP协议
- ✅ 支持实时龙虎榜数据获取
- ✅ 支持历史数据查询
- ✅ 完整的错误处理和日志记录
- ✅ 多种数据输出格式
- ✅ 实时监控功能

## 📞 技术支持

如果遇到问题：

1. **查看文档** - 阅读README.md和使用说明.md
2. **运行测试** - 使用test_tcp_connection.py测试连接
3. **查看日志** - 检查longhu_spider.log文件
4. **简单测试** - 运行simple_test.py验证功能

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

---

**🎯 项目目标：提供一个简单、可靠、高效的开盘啦龙虎榜数据获取工具**

**⚡ 核心优势：基于真实TCP协议，无需复杂配置，开箱即用**
