#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试开盘啦龙虎榜爬虫
"""

from kaipanla_longhu_spider import KaiPanLaLongHuSpider
import json

def test_spider():
    """测试爬虫功能"""
    print("=== 开盘啦龙虎榜爬虫测试 ===")
    
    # 创建爬虫实例
    spider = KaiPanLaLongHuSpider()
    
    # 测试数据（模拟API返回的数据格式）
    test_data = [
        {
            'code': '000001',
            'name': '平安银行',
            'price': 12.50,
            'change_percent': 5.2,
            'amount': 1500000000,
            'volume': 120000000,
            'reason': '日涨幅偏离值达7%',
            'date': '2024-01-15'
        },
        {
            'code': '000002',
            'name': '万科A',
            'price': 8.90,
            'change_percent': -3.8,
            'amount': 800000000,
            'volume': 90000000,
            'reason': '日跌幅偏离值达7%',
            'date': '2024-01-15'
        }
    ]
    
    print("1. 测试CSV保存功能...")
    if spider.save_to_csv(test_data, 'test_longhu'):
        print("✓ CSV保存测试通过")
    else:
        print("✗ CSV保存测试失败")
    
    print("\n2. 测试JSON保存功能...")
    if spider.save_to_json(test_data, 'test_longhu'):
        print("✓ JSON保存测试通过")
    else:
        print("✗ JSON保存测试失败")
    
    print("\n3. 测试API请求功能...")
    # 注意：由于没有真实的API接口，这里会失败，但可以测试请求逻辑
    result = spider.get_realtime_longhu()
    if isinstance(result, list):
        print("✓ API请求逻辑正常（返回空列表是正常的，因为API接口需要实际抓包获取）")
    else:
        print("✗ API请求逻辑异常")
    
    print("\n=== 测试完成 ===")
    print("注意：要获取真实数据，需要：")
    print("1. 使用抓包工具（如Fiddler、Charles）获取真实的API接口")
    print("2. 更新代码中的API URL")
    print("3. 可能需要添加认证信息或特殊请求头")

if __name__ == "__main__":
    test_spider()
